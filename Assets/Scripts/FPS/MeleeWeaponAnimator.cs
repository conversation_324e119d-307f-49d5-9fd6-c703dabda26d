// 8/14/2025 AI-Tag
// This was created with the help of <PERSON>, a Unity Artificial Intelligence product.

using UnityEngine;

public class MeleeWeaponAnimator : MonoBehaviour
{
    [Header("Swing Animation Settings")]
    public float swingSpeed = 8.0f;
    public float swingAmplitude = 1.2f;

    [Header("Idle Bob Settings")]
    public float idleSpeed = 1.5f;
    public float idleAmplitude = 0.05f;

    [Header("Position Settings")]
    public Transform weaponTransform;
    public Vector3 idleOffset;
    public Vector3 swingOffset;

    private float swingTimer;
    private float idleTimer;
    private bool isSwinging;

    private Vector3 initialPosition;

    void Start()
    {
        if (weaponTransform == null)
        {
            weaponTransform = transform;
        }

        initialPosition = weaponTransform.localPosition;
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Mouse0))
        {
            TriggerSwing();
            Debug.Log("Swing Triggered");
        }
        
        if (isSwinging)
        {
            PerformSwingAnimation();
        }
        else
        {
            PerformIdleAnimation();
        }
    }

    public void TriggerSwing()
    {
        if (!isSwinging)
        {
            isSwinging = true;
            swingTimer = 0.0f;
        }
    }

    private void PerformSwingAnimation()
    {
        swingTimer += Time.deltaTime * swingSpeed;
        float swingProgress = SwingCurve(swingTimer);

        Vector3 swingPosition = initialPosition + swingOffset * swingProgress * swingAmplitude;
        weaponTransform.localPosition = swingPosition;

        if (swingTimer >= 1.0f)
        {
            isSwinging = false;
            swingTimer = 0.0f;
        }
    }

    private void PerformIdleAnimation()
    {
        idleTimer += Time.deltaTime * idleSpeed;
        float idleProgress = IdleCurve(idleTimer % 1.0f);

        Vector3 idlePosition = initialPosition + idleOffset * idleProgress * idleAmplitude;
        weaponTransform.localPosition = idlePosition;
    }

    private float SwingCurve(float t)
    {
        // Starts fast, slows down near the end for better feel
        return Mathf.Sin(t * Mathf.PI);
    }

    private float IdleCurve(float t)
    {
        // Smooth oscillation for idle movement
        return Mathf.Sin(t * Mathf.PI * 2);
    }
}