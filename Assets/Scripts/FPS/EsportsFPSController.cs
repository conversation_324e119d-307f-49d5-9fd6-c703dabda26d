// 8/13/2025 AI-Tag
// This was created with the help of Assistant, a Unity Artificial Intelligence product.

using UnityEngine;

[RequireComponent(typeof(CharacterController))]
public class EsportsFPSController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float walkSpeed = 5f;
    public float sprintSpeed = 10f;
    public float crouchSpeed = 2.5f;
    public float gravity = -20f;
    public float jumpHeight = 1.5f;

    [Header("Axis Movement Multipliers")]
    public float forwardSpeedMultiplier = 1.0f;
    public float strafeSpeedMultiplier = 0.8f;
    public float backwardSpeedMultiplier = 0.6f;

    [Header("Camera Settings")]
    public Transform playerCamera;
    public float mouseSensitivityX = 1f;
    public float mouseSensitivityY = 1f;
    public float crouchHeight = 1f;
    public float normalHeight = 2f;
    public float crouchTransitionSpeed = 10f;

    [Header("Air Control")]
    public float airControl = 0.5f;

    private CharacterController controller;
    private Vector3 velocity;
    private float currentSpeed;
    private float xRotation = 0f;
    private bool isCrouching = false;

    void Start()
    {
        controller = GetComponent<CharacterController>();
        Cursor.lockState = CursorLockMode.Locked;
        
        QualitySettings.vSyncCount = 0; // Disable VSync
        Application.targetFrameRate = 240; // Lock to 60 FPS
    }

    void Update()
    {
        HandleMouseLook();
        HandleMovement();
        HandleCrouch();
    }

    void HandleMouseLook()
    {
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivityX;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivityY;

        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -90f, 90f);

        playerCamera.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        transform.Rotate(Vector3.up * mouseX);
    }

    void HandleMovement()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");

        Vector3 move = transform.right * horizontal * strafeSpeedMultiplier +
                       transform.forward * vertical * (vertical > 0 ? forwardSpeedMultiplier : backwardSpeedMultiplier);

        if (controller.isGrounded)
        {
            if (Input.GetKey(KeyCode.LeftShift))
            {
                currentSpeed = sprintSpeed;
            }
            else if (isCrouching)
            {
                currentSpeed = crouchSpeed;
            }
            else
            {
                currentSpeed = walkSpeed;
            }

            // Apply horizontal movement to velocity when grounded
            velocity.x = move.x * currentSpeed;
            velocity.z = move.z * currentSpeed;
            velocity.y = -2f; // Reset Y velocity when grounded

            if (Input.GetButtonDown("Jump"))
            {
                velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            }
        }
        else
        {
            // Apply air control to horizontal movement
            velocity.x = Mathf.Lerp(velocity.x, move.x * currentSpeed, airControl * Time.deltaTime);
            velocity.z = Mathf.Lerp(velocity.z, move.z * currentSpeed, airControl * Time.deltaTime);
        }

        // Apply gravity
        velocity.y += gravity * Time.deltaTime;

        // Move the character with the combined velocity
        controller.Move(velocity * Time.deltaTime);
    }

    void HandleCrouch()
    {
        if (Input.GetKeyDown(KeyCode.LeftControl))
        {
            isCrouching = !isCrouching;
        }

        float targetHeight = isCrouching ? crouchHeight : normalHeight;
        controller.height = Mathf.Lerp(controller.height, targetHeight, crouchTransitionSpeed * Time.deltaTime);
    }
}